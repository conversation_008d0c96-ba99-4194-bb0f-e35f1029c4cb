@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(calc(-50%));
  }
}

.animate-scroll {
  animation: scroll 40s linear infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 225 45% 23%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222 47% 11%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222 47% 11%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222 47% 11%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;

    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 225 45% 33%;
    --primary-foreground: 210 40% 98%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-galano;
  }
}

@font-face {
  font-family: "Galano Grotesque";
  src: url("/fonts/galano-grotesque-medium.woff2") format("woff2");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Galano Grotesque";
  src: url("/fonts/galano-grotesque-semibold.woff2") format("woff2");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Galano Grotesque";
  src: url("/fonts/galano-grotesque-bold.woff2") format("woff2");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

.font-galano {
  font-family: "Galano Grotesque", sans-serif;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom utility classes */
.section-padding {
  @apply py-16 md:py-24 lg:py-32;
}

.container-custom {
  @apply container px-4 md:px-6 max-w-7xl mx-auto;
}

/* Sentence styling - each sentence on a new line with justified text */
p {
  text-align: justify;
  text-justify: inter-word;
}

/* This will be used by our sentence splitter component */
.sentence {
  display: block;
  width: 100%;
  text-align: justify;
  text-justify: inter-word;
  margin-bottom: 0.5rem;
}

/* Add this to maintain spacing between paragraphs */
p + p {
  margin-top: 1.5rem;
}

/* Hero section styles */
.hero-gradient {
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  padding: 1rem 0;
}

.glass {
  background: rgba(59, 130, 246, 0.2);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
} 

.blob {
  position: absolute;
  width: 300px;
  height: 300px;
  background: rgba(59, 130, 246, 0.4);
  border-radius: 50%;
  filter: blur(80px);
  animation: float 8s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-20px) scale(1.05);
  }
}

@keyframes floating {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.floating {
  animation: floating 3s ease-in-out infinite;
} 











/* Hero section styling */
.hero-gradient {
  background: linear-gradient(135deg, #202957 0%, #1e3a8a 100%);
  position: relative;
}

.hero-gradient::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("/abstract-geometric-shapes.png");
  background-size: cover;
  background-position: center;
  opacity: 0.1;
  z-index: 0;
}

.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.floating {
  animation: float 4s ease-in-out infinite;
}

.blob {
  position: absolute;
  width: 300px;
  height: 300px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  filter: blur(80px);
  z-index: 0;
}



/* test styles */






/* Force center alignment for specific text that should never be justified */
.stats-intro-text {
  text-align: center !important;
  text-justify: none !important;
}

.stats-intro-text.sentence,
.stats-intro-text .sentence {
  display: inline !important;
  text-align: center !important;
  width: auto !important;
  margin-bottom: 0 !important;
}

.keep-words-together {
  word-break: keep-all !important;
  overflow-wrap: normal !important;
  word-wrap: normal !important;
  white-space: normal !important;
  hyphens: none !important;
  text-wrap: nowrap !important;
  display: inline-block !important;
}






.no-word-gaps {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* .center {
  display: flex;
  justify-content: center;
  text-align: center !important;
} */


.process {
  word-break: keep-all !important;
  overflow-wrap: normal !important;
  word-wrap: normal !important;
  white-space: normal !important;
  hyphens: none !important;
  text-wrap: wrap !important;
  display: inline-block !important;
}
